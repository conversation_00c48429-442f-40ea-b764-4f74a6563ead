import 'package:cat_player/models/preset_item.dart';
import 'package:flutter/material.dart';

class PresetListWidget extends StatelessWidget {
  final List<PresetItem> filteredPresetList;
  final List<PresetItem> presetList;
  final PresetItem? currentPreset;
  final bool isLoading;
  final Function(PresetItem) onSelectPreset;
  final Function(PresetItem) onEditPreset;
  final Function(PresetItem) onRemovePreset;

  const PresetListWidget({
    super.key,
    required this.filteredPresetList,
    required this.presetList,
    required this.currentPreset,
    required this.isLoading,
    required this.onSelectPreset,
    required this.onEditPreset,
    required this.onRemovePreset,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Theme.of(context).brightness == Brightness.dark
          ? Colors.transparent
          : Theme.of(context).cardColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Icon(
                  Icons.settings_applications,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Presets (${filteredPresetList.length})',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          Expanded(
            child: isLoading
                ? const Center(child: CircularProgressIndicator())
                : filteredPresetList.isEmpty
                    ? Center(
                        child: Text(
                          presetList.isEmpty
                              ? 'No presets available'
                              : 'No presets match your search',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface
                                        .withOpacity(0.7),
                                  ),
                        ),
                      )
                    : ListView.builder(
                        itemCount: filteredPresetList.length,
                        itemBuilder: (context, index) {
                          final preset = filteredPresetList[index];
                          final isCurrentPreset = currentPreset == preset;

                          final extension = _getExtensionFromUrl(preset.url);
                          return ListTile(
                            title: Text(
                              preset.name,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontWeight: isCurrentPreset
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                color: isCurrentPreset
                                    ? Theme.of(context).colorScheme.primary
                                    : null,
                              ),
                            ),
                            subtitle: Text(
                              preset.url,
                              style: Theme.of(context).textTheme.bodySmall,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (extension.isNotEmpty)
                                  Chip(
                                    label: Text(extension),
                                    padding: EdgeInsets.zero,
                                    labelStyle: const TextStyle(
                                      fontSize: 12,
                                    ),
                                    visualDensity: VisualDensity.compact,
                                  ),
                                const SizedBox(width: 8),
                                IconButton(
                                  icon: const Icon(Icons.edit),
                                  onPressed: () => onEditPreset(preset),
                                  tooltip: 'Edit preset',
                                ),
                                IconButton(
                                  icon: const Icon(Icons.delete_outline),
                                  onPressed: () => onRemovePreset(preset),
                                  tooltip: 'Remove preset',
                                ),
                              ],
                            ),
                            onTap: () => onSelectPreset(preset),
                            tileColor: isCurrentPreset
                                ? Theme.of(context)
                                    .colorScheme
                                    .primary
                                    .withOpacity(0.1)
                                : null,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(
                                color: isCurrentPreset
                                    ? Theme.of(context).colorScheme.primary
                                    : Colors.transparent,
                                width: 1,
                              ),
                            ),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }

  String _getExtensionFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final path = uri.path;
      if (path.contains('.')) {
        final extension = path.split('.').last.toUpperCase();
        // Limit the length to avoid long, invalid extensions
        return extension.length <= 4 ? extension : '';
      }
    } catch (e) {
      // Ignore parsing errors
    }
    return '';
  }
}
