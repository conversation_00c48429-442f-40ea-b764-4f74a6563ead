import 'package:flutter/material.dart';

class NavigationDrawer extends StatelessWidget {
  const NavigationDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: <Widget>[
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
            ),
            child: Text(
              'Navigation',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onPrimary,
                fontSize: 24,
              ),
            ),
          ),
          ListTile(
            leading: const Icon(Icons.info_outline),
            title: const Text('Information'),
            onTap: () {
              // Handle information tap
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.public),
            title: const Text('Website'),
            onTap: () {
              // Handle website tap
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.facebook),
            title: const Text('Facebook'),
            onTap: () {
              // Handle Facebook tap
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.send),
            title: const Text('Telegram'),
            onTap: () {
              // Handle Telegram tap
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.mail_outline),
            title: const Text('Email'),
            onTap: () {
              // Handle Email tap
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.mark_email_unread_outlined),
            title: const Text('Notifications'),
            onTap: () {
              // Handle notifications tap
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.rate_review_outlined),
            title: const Text('Rate Us'),
            onTap: () {
              // Handle rate us tap
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('Share'),
            onTap: () {
              // Handle share tap
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.mic_none),
            title: const Text('Audio Settings'),
            onTap: () {
              // Handle audio settings tap
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }
}
