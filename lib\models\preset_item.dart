class PresetItem {
  final String id;
  final String name;
  final String url;
  final String? agent;
  final String? referer;
  final DateTime dateAdded;

  PresetItem({
    required this.id,
    required this.name,
    required this.url,
    this.agent,
    this.referer,
    DateTime? dateAdded,
  }) : dateAdded = dateAdded ?? DateTime.now();

  PresetItem copyWith({
    String? name,
    String? url,
    String? agent,
    String? referer,
  }) {
    return PresetItem(
      id: id,
      name: name ?? this.name,
      url: url ?? this.url,
      agent: agent ?? this.agent,
      referer: referer ?? this.referer,
      dateAdded: dateAdded,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PresetItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PresetItem(id: $id, name: $name, url: $url)';
  }
}
