import 'package:flutter/material.dart';

class AppTheme {
  // Light Theme Colors
  static const Color _lightPrimary = Color(0xFF6A1B9A);
  static const Color _lightSecondary = Color(0xFF8E24AA);
  static const Color _lightSurface = Colors.white;
  static const Color _lightOnPrimary = Colors.white;
  static const Color _lightOnSecondary = Colors.white;
  static const Color _lightOnSurface = Color(0xFF1A1A1A);
  static const Color _lightOutline = Color(0xFFE0E0E0);
  static const Color _lightScaffoldBackground = Colors.white;
  static const Color _lightCardBackground = Colors.white;
  static const Color _lightIcon = Color(0xFF424242);
  static const Color _lightListTileText = Color(0xFF1A1A1A);
  static const Color _lightDivider = Color(0xFFE0E0E0);
  static const Color _lightInputFieldFill = Color(0xFFF5F5F5);

  // Dark Theme Colors
  static const Color _darkPrimary = Colors.deepPurple;
  static const Color _darkSurface = Color(0xFF121212);
  static const Color _darkOnPrimary = Colors.white;
  static const Color _darkOnSurface = Colors.white;
  static const Color _darkCardBackground = Color(0xFF1E1E1E);
  static const Color _darkIcon = Colors.grey;
  static const Color _darkListTileText = Colors.white;

  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: const ColorScheme.light(
        primary: _lightPrimary,
        secondary: _lightSecondary,
        surface: _lightSurface,
        onPrimary: _lightOnPrimary,
        onSecondary: _lightOnSecondary,
        onSurface: _lightOnSurface,
        outline: _lightOutline,
      ),
      scaffoldBackgroundColor: _lightScaffoldBackground,
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.white.withValues(alpha: 0.95),
        foregroundColor: _lightOnSurface,
        titleTextStyle: const TextStyle(
          color: _lightOnSurface,
          fontSize: 20,
          fontWeight: FontWeight.w500,
        ),
        iconTheme: const IconThemeData(color: _lightIcon),
        elevation: 0,
        scrolledUnderElevation: 1,
      ),
      cardTheme: CardThemeData(
        color: _lightCardBackground,
        shadowColor: Colors.black.withValues(alpha: 0.12),
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: const BorderSide(color: _lightOutline, width: 0.5),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: _lightPrimary,
          foregroundColor: _lightOnPrimary,
          shadowColor: _lightPrimary.withValues(alpha: 0.3),
          elevation: 3,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: _lightPrimary,
        foregroundColor: _lightOnPrimary,
      ),
      iconTheme: const IconThemeData(color: _lightIcon),
      listTileTheme: const ListTileThemeData(
        textColor: _lightListTileText,
        iconColor: _lightIcon,
        tileColor: _lightSurface,
      ),
      dividerTheme: const DividerThemeData(color: _lightDivider),
      inputDecorationTheme: InputDecorationTheme(
        fillColor: _lightInputFieldFill,
        filled: true,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: _lightOutline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: _lightOutline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: _lightPrimary, width: 2),
        ),
      ),
    );
  }

  static ThemeData get darkTheme {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: Colors.deepPurple,
      brightness: Brightness.dark,
    );

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: colorScheme,
      scaffoldBackgroundColor: Colors.black,
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.black.withOpacity(0.7),
        foregroundColor: colorScheme.onSurface,
        titleTextStyle: const TextStyle(
          color: _darkOnSurface,
          fontSize: 20,
          fontWeight: FontWeight.w500,
        ),
        iconTheme: const IconThemeData(color: _darkIcon),
        elevation: 0,
        scrolledUnderElevation: 1,
      ),
      cardTheme: CardThemeData(
        color: _darkCardBackground,
        shadowColor: Colors.black.withValues(alpha: 0.3),
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: _darkPrimary,
          foregroundColor: _darkOnPrimary,
          elevation: 3,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: _darkPrimary,
        foregroundColor: _darkOnPrimary,
      ),
      iconTheme: const IconThemeData(color: _darkIcon),
      listTileTheme: const ListTileThemeData(
        textColor: _darkListTileText,
        iconColor: _darkIcon,
        tileColor: _darkSurface,
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: Colors.black.withOpacity(0.7),
        unselectedItemColor: Colors.white70,
      ),
    );
  }
}
